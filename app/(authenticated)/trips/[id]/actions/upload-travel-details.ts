"use server"

import { put } from "@vercel/blob"
import { redirect } from "next/navigation"

// Maximum file size: 5MB
const MAX_FILE_SIZE = 5 * 1024 * 1024

// Allowed file types
const ALLOWED_TYPES = ["image/jpeg", "image/jpg", "image/png", "image/webp"]

export interface UploadTravelDetailsResult {
  success: boolean
  url?: string
  error?: string
}

export async function uploadTravelDetailsAction(
  tripId: string,
  userId: string,
  type: "flight" | "accommodation",
  formData: FormData
): Promise<UploadTravelDetailsResult> {
  try {
    // Validate user ID is provided (authentication should be handled by the calling component)
    if (!userId) {
      redirect("/login")
    }

    const file = formData.get("file") as File

    // Validate required fields
    if (!file) {
      return {
        success: false,
        error: "No file provided",
      }
    }

    if (!tripId) {
      return {
        success: false,
        error: "Trip ID is required",
      }
    }

    if (!type || !["flight", "accommodation"].includes(type)) {
      return {
        success: false,
        error: "Type must be either 'flight' or 'accommodation'",
      }
    }

    // Validate file type
    if (!ALLOWED_TYPES.includes(file.type)) {
      return {
        success: false,
        error: "Invalid file type. Only JPEG, PNG, and WebP images are allowed.",
      }
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      return {
        success: false,
        error: "File size too large. Maximum size is 5MB.",
      }
    }

    // Generate a unique filename
    const timestamp = Date.now()
    const fileExtension = file.name.split(".").pop() || "jpg"
    const filename = `travel-details/${tripId}/${userId}/${type}-${timestamp}.${fileExtension}`

    try {
      // Upload to Vercel Blob
      const blob = await put(filename, file, {
        access: "public",
        token: process.env.BLOB_READ_WRITE_TOKEN,
      })

      return {
        success: true,
        url: blob.url,
      }
    } catch (uploadError) {
      console.error("Vercel Blob upload error:", uploadError)
      return {
        success: false,
        error: "Failed to upload image to storage",
      }
    }
  } catch (error) {
    console.error("Travel details upload error:", error)
    return {
      success: false,
      error: "Internal server error",
    }
  }
}
